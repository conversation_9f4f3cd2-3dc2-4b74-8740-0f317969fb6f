import logging
import sys
from logging.handlers import <PERSON><PERSON><PERSON><PERSON><PERSON>, TimedRotatingFileHandler

from util import crypt_util


def setup_logger(log_config, mail_config):
    """
    ロガーを設定する。
    :param log_config: ログ設定情報
    :param mail_config: メール設定情報
    :return: ロガー
    """
    # ファイルハンドラを作成
    file_handler = TimedRotatingFileHandler(
        filename=log_config["file_handler"]["path"],
        when=log_config["file_handler"]["when"],
        interval=log_config["file_handler"]["interval"],
        backupCount=log_config["file_handler"]["backup_count"],
        encoding=log_config["file_handler"]["encoding"],
    )
    file_handler.setLevel(log_config["file_handler"]["level"])
    file_handler.setFormatter(
        logging.Formatter(
            fmt=log_config["file_handler"]["format"],
            datefmt=log_config["datetime_format"],
        )
    )

    # コンソールハンドラを作成
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_config["console_handler"]["level"])
    console_handler.setFormatter(
        logging.Formatter(
            fmt=log_config["console_handler"]["format"],
            datefmt=log_config["datetime_format"],
        )
    )

    # メールハンドラを作成
    email_handler = SMTPHandler(
        mailhost=(mail_config["smtp_server"], mail_config["smtp_port"]),
        fromaddr=mail_config["from_email"],
        toaddrs=mail_config["to_email"],
        subject=log_config["smtp_handler"]["subject"],
        credentials=(
            mail_config["login"],
            crypt_util.decrypt(mail_config["encrypted_password"]),
        ),
        secure=(),
    )
    email_handler.setLevel(log_config["smtp_handler"]["level"])
    email_handler.setFormatter(
        logging.Formatter(
            fmt=log_config["smtp_handler"]["format"],
            datefmt=log_config["datetime_format"],
        )
    )

    # ルートロガーの設定
    logging.basicConfig(
        level=get_log_level(log_config["level"]),
        handlers=[file_handler, console_handler, email_handler],
    )

    # ロガーを取得
    logger = logging.getLogger()

    return logger


def get_log_level(log_level_string):
    """
    ログレベルを取得する。
    :param log_level_string: ログレベルの文字列
    :return: ログレベル
    """
    log_levels = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL,
    }

    return log_levels[log_level_string]
