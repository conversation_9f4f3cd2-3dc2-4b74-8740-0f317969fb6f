import sys

import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from util import config_util, csv_util, db_util, excel_util, log_util
from version import __version__

# 設定ファイル
CONFIG_FILE = "config/config.yaml"


def main():
    try:
        # 設定ファイルの読み込み
        config = config_util.get_config(CONFIG_FILE)

        # ログ設定
        global logger
        logger = log_util.setup_logger(config["log"], config["mail"])

        logger.info(f"処理開始　バージョン：{__version__}")
        logger.info(f"設定情報：{config}")

        input_file_name = ""
        if len(sys.argv) > 1:
            input_file_name = sys.argv[1]
            logger.info(f"読込ファイル名：{input_file_name}")

        # デバッグモード
        debug_mode = config["debug_mode"]
        logger.info(f"デバッグモード：{debug_mode}")

        # 品番リストの読み込み
        code_list_excel = config["files"]["code_list_excel"]
        df_code_list = pd.read_excel(code_list_excel, dtype=str, header=0)

        # 商品名と品番の前後のスペースを削除
        df_code_list["商品名"] = df_code_list["商品名"].str.strip()
        df_code_list["品番"] = df_code_list["品番"].str.strip()

        # デバッグ用
        if debug_mode:
            input_file_name = "item_list.csv"

        # 処理時間短縮のため、CSVファイルが指定されている場合はWEBサイトから商品リストを取得しない
        if input_file_name:
            # CSVファイルから商品リストを取得
            logger.info(f"CSVファイルから商品リストを取得：{input_file_name}")
            item_list = csv_util.read_csv(input_file_name)
        else:
            # WEBサイトから商品リストを取得
            logger.info("WEBサイトから商品リストを取得")
            item_list = get_item_list(config)

        logger.info(f"商品件数：{len(item_list)}")

        # 商品リストが空の場合はエラー
        if len(item_list) == 0:
            raise Exception("WEBサイトから商品が取得できませんでした。")

        # 商品リストから商品情報を編集
        logger.info("取得した商品リストから必要な情報を抽出")
        item_datas = get_item_datas(item_list, df_code_list, config)

        # 販売管理の商品コードを追加
        logger.info("販売管理の商品コードを追加")
        item_datas = add_hanbai_code(item_datas, config)

        # excelファイル出力
        excel_file = config["files"]["scraped_data_excel"]
        logger.info(f"Excelファイルに保存：{excel_file}")
        excel_util.save_excel(item_datas, excel_file)

        logger.info("正常終了しました")
    except Exception:
        logger.exception("エラーが発生しました。処理を中止します。")
        sys.exit(1)


def get_selenium_driver(config):
    """
    Seleniumのドライバーを取得
    :param config: 設定情報
    :return: Seleniumのドライバー
    """
    # Seleniumのセットアップ
    options = Options()
    for option in config["selenium"]["options"]:
        options.add_argument(option)
    logger.info(f"Chromeオプション：{options.arguments}")

    driver = webdriver.Chrome(options=options)

    return driver


def get_url(page, config):
    """
    URLを取得
    :param page: ページ番号
    :param config: 設定情報
    :return: URL
    """
    # 対象URLを作成
    base_url = config["syuppin"]["url"]
    url_params = config["syuppin"]["url_params"]
    fragment = config["syuppin"]["fragment"]

    # 対象メーカー
    brands = config["syuppin"]["brand"]
    makers = [brand["maker"] for brand in brands]
    maker = ",".join(makers)

    url = base_url
    if url_params:  # パラメータがある場合
        param_list = [f"{key}={value}" for key, value in url_params.items()]
        if page > 1:
            param_list.append(f"page={page}")
        url += "?" + "&".join(param_list)
        url += f"&maker={maker}"
    if fragment:  # フラグメントがある場合
        url += f"#{fragment}"  # 対象URLを開く

    return url


def open_url(driver, url, config):
    """
    指定のURLを開く
    """
    logger.info(f"URLを開く：{url}")
    driver.get(url)
    # 最大待機時間設定（秒）
    wait = WebDriverWait(driver, config["selenium"]["max_wait_time"])
    # 条件が満たされるまで待機（指定の要素がページに表示される）
    wait.until(EC.presence_of_element_located((By.CLASS_NAME, "FP-resultWrap")))


def get_item_list(config):
    """
    WEBサイトから商品リストを取得
    :param config: 設定情報
    :return: 商品リスト
    """
    # Seleniumのドライバー
    driver = None

    try:
        # Seleniumのドライバーを取得
        driver = get_selenium_driver(config)

        # 対象URLを作成
        page = 1
        url = get_url(page, config)

        # 1ページ目を開く
        logger.info("1ページ目を開く")
        open_url(driver, url, config)

        # 総ページ数を取得
        total_pages = get_total_pages(driver, config)

        # 商品情報
        item_list = []

        # 商品リストを取得してデータを作成
        for current_page in range(1, total_pages + 1):
            if current_page > 1:
                driver.quit()
                driver = get_selenium_driver(config)

                # 次のページのURLを作成
                next_url = get_url(current_page, config)
                # 指定のURLを開く
                logger.info(f"{current_page}ページ目を開く")
                open_url(driver, next_url, config)

            # 商品リストを取得
            items = driver.find_elements(By.CLASS_NAME, "FP-resultItem")

            for item in items:
                product_name = (
                    item.find_element(By.CLASS_NAME, "FP-resultItem__name").text
                    if len(item.find_elements(By.CLASS_NAME, "FP-resultItem__name")) > 0
                    else ""
                )

                # 商品名が空の場合はスキップ
                if product_name == "":
                    continue

                price = (
                    item.find_element(By.CLASS_NAME, "FP-resultItem__price").text
                    if len(item.find_elements(By.CLASS_NAME, "FP-resultItem__price"))
                    > 0
                    else ""
                )

                tax = (
                    item.find_element(By.CLASS_NAME, "FP-resultItem__tax").text
                    if len(item.find_elements(By.CLASS_NAME, "FP-resultItem__tax")) > 0
                    else ""
                )

                product_id = (
                    item.find_element(By.CLASS_NAME, "FP-resultItem__id").text
                    if len(item.find_elements(By.CLASS_NAME, "FP-resultItem__id")) > 0
                    else ""
                )

                sold_out = (
                    item.find_element(
                        By.CLASS_NAME, "FP-resultItem__priceHighlight"
                    ).text
                    if len(
                        item.find_elements(
                            By.CLASS_NAME, "FP-resultItem__priceHighlight"
                        )
                    )
                    > 0
                    else ""
                )

                product_url = ""
                a_tag = item.find_element(By.TAG_NAME, "a")
                if a_tag:  # aタグが存在するか確認
                    href = a_tag.get_attribute("href")
                    if href:  # href属性が存在するか確認
                        product_url = href

                image_url = ""
                img_tag = item.find_element(By.TAG_NAME, "img")
                if img_tag:  # imgタグが存在するか確認
                    src = img_tag.get_attribute("src")
                    if src:  # src属性が存在するか確認
                        image_url = src

                item_list.append(
                    {
                        "product_name": product_name,
                        "price": price,
                        "tax": tax,
                        "product_id": product_id,
                        "sold_out": sold_out,
                        "product_url": product_url,
                        "image_url": image_url,
                    }
                )
    finally:
        # ドライバーを閉じる
        if driver:
            driver.quit()

    # csvファイル出力
    csv_util.save_csv(item_list, config["files"]["item_list_csv"])

    return item_list


def get_total_pages(driver, config):
    """
    総ページ数を取得
    :param driver: Seleniumのドライバー
    :param config: 設定情報
    :return: 総ページ数
    """
    # 総商品数を取得
    total_items = driver.find_element(By.CLASS_NAME, "FP-floatingNum__num").text
    logger.info(f"総商品数：{total_items}")

    # 総ページ数を計算
    total_pages = int(total_items) // int(config["syuppin"]["url_params"]["rows"]) + 1
    logger.info(f"総ページ数：{total_pages}")

    return total_pages


def get_item_datas(item_list, df_code_list, config):
    """
    商品リストから商品情報を抽出
    :param item_list: 商品リスト
    :param df_code_list: 品番リスト
    :param config: 設定情報
    :return: 商品情報リスト
    """
    # 商品情報
    item_datas = []

    for item in item_list:
        # ブランド名（英語）
        brand_name_english = ""
        for brand in config["syuppin"]["brand"]:
            if brand["name_english"] in item["product_name"]:
                brand_name_english = brand["name_english"]
                break

        # ブランド名（日本語）
        brand_name_japanese = item["product_name"].split()[1]
        brand_name_japanese = ""
        for brand in config["syuppin"]["brand"]:
            if brand["name_japanese"] in item["product_name"]:
                brand_name_japanese = brand["name_japanese"]
                break

        # 商品名
        product_name = item["product_name"]

        # 品番
        product_code = get_product_code(item["product_name"], df_code_list)

        # 価格
        price_text = item["price"]
        # 税
        tax_text = item["tax"]

        # 価格を編集
        # tax_textに一致する文字列をprice_textから削除
        price_text = price_text.replace(tax_text, "").strip()
        # 先頭の「￥」とカンマを削除
        price = price_text.replace("￥", "").replace(",", "").strip()

        # 税を編集
        # 括弧を削除
        tax = tax_text.replace("(", "").replace(")", "").strip()

        # ID
        product_id_text = item["product_id"]
        # IDを編集
        # ID:と括弧を削除
        product_id = product_id_text.replace("[ID:", "").replace("]", "").strip()

        # sold_out
        if item["sold_out"] == "":
            sold_out = "在庫あり"
        elif item["sold_out"] == "SOLD OUT":
            sold_out = "SOLD OUT"
        elif item["sold_out"] == "商談中":
            sold_out = "商談中"
        else:
            # SOLD OUTの場所に価格が設定されている場合は在庫あり
            sold_out = "在庫あり"
            price_text = item["sold_out"].replace(tax_text, "").strip()
            price = price_text.replace("￥", "").replace(",", "").strip()

        product_url = item["product_url"]

        image_url = item["image_url"]

        item_datas.append(
            {
                "ブランド名（英語）": brand_name_english,
                "ブランド名（日本語）": brand_name_japanese,
                "商品名": product_name,
                "品番": product_code,
                "価格": price,
                "税": tax,
                "ID": product_id,
                "SOLD OUT": sold_out,
                "商品URL": product_url,
                "画像URL": image_url,
            }
        )

    return item_datas


def get_product_code(product_name, df_code_list):
    """
    商品名から品番を取得
    :param product_name: 商品名
    :param df_code_list: 品番リスト
    :return: 品番
    """
    # 検索する商品名の前後のスペースを削除
    product_name = product_name.strip()

    # 商品名で検索
    result = df_code_list[df_code_list["商品名"] == product_name]["品番"]

    # 品番が見つからない場合は空白
    product_code = result.iloc[0] if not result.empty else ""

    return product_code


def add_hanbai_code(item_datas, config):
    """
    販売管理の商品コードを追加
    :param item_datas: 商品情報リスト
    :param config: 設定情報
    :return: 商品情報リスト
    """
    # 編集後の商品情報リスト
    result_list = []

    # 優先ブランドコードのリスト
    priority_brand_code_list = []
    for brand in config["syuppin"]["brand"]:
        if brand["priority_brand_code"]:
            if brand["priority_brand_code"]:
                priority_brand_code_list.extend(brand["priority_brand_code"])

    # SQL Serverに接続
    with db_util.get_connection(config["db"]) as conn:
        # 販売管理コード取得
        for item_data in item_datas:
            product_code = item_data["品番"]
            # 販売管理コードのリストを取得
            hanbai_code_list = get_hanbai_code_list(product_code, conn)
            # 販売管理コードを取得
            hanbai_code = get_hanbai_code(hanbai_code_list, priority_brand_code_list)
            # 品番と価格の間に挿入
            result = {}
            for key, value in item_data.items():
                result[key] = value
                if key == "品番":
                    result["販売管理コード"] = hanbai_code
            result_list.append(result)

    return result_list


def get_hanbai_code(hanbai_code_list, priority_brand_code_list):
    """
    商品コードを取得
    :param hanbai_code_list: 販売管理コードのリスト
    :param priority_brand_code_list: 優先ブランドコードのリスト
    :return: 販売管理コード
    """
    # 販売コードのリストが空の場合は空白を返す
    if not hanbai_code_list:
        return ""

    # 販売管理コードのリストが1つの場合はそのコードを返す
    if len(hanbai_code_list) == 1:
        return hanbai_code_list[0]

    # 先頭の文字列が優先ブランドコードと一致する場合は、先に一致する販売管理コードを返す
    for priority_brand_code in priority_brand_code_list:
        for hanbai_code in hanbai_code_list:
            if hanbai_code[0:3] == priority_brand_code:
                return hanbai_code

    # 複数の販売管理コードがある場合は改行コードで連結
    hanbai_code = "\n".join(hanbai_code_list)
    return hanbai_code


def get_hanbai_code_list(product_code, conn):
    """
    商品コードのリストを取得
    :param product_code: 商品コード
    :param conn: データベース接続
    :return: 販売管理コードのリスト
    """
    # 販売管理コードのリスト
    hanbai_code_list = []

    # 商品コードが空の場合は空白を返す
    if product_code == "":
        return hanbai_code_list

    # SQL文
    sql = "SELECT 商品CD FROM MSYOHINMS WHERE 型番_ﾒｰｶｰ品番 = ?"

    # SQL文を実行
    rows = db_util.fetchall(conn, sql, str(product_code))

    if rows:
        for row in rows:
            # 販売管理コードを整形
            hanbai_code = format_hanbai_code(row["商品CD"])
            # 販売管理コードをリストに追加
            hanbai_code_list.append(hanbai_code)

    return hanbai_code_list


def format_hanbai_code(hanbai_code):
    """
    販売管理コードを整形
    :param hanbai_code: 販売管理コード
    :return: 整形後の販売管理コード
    """
    # 販売管理コードをハイフンの位置で分割
    part1 = hanbai_code[0:3]
    part2 = hanbai_code[3:18]
    part3 = hanbai_code[18:23]
    part4 = hanbai_code[23:28]
    part5 = hanbai_code[28:31]
    part6 = hanbai_code[31:34]
    part7 = hanbai_code[34:37]
    part8 = hanbai_code[37:40]

    # ハイフンで連結して完成
    formatted_code = "-".join([part1, part2, part3, part4, part5, part6, part7, part8])

    return formatted_code


if __name__ == "__main__":
    main()
