import sys

import pandas as pd
from playwright.sync_api import sync_playwright

from util import config_util, csv_util, db_util, excel_util, log_util
from version import __version__

# 設定ファイル
CONFIG_FILE = "config/config.yaml"


def main():
    try:
        # 設定ファイルの読み込み
        config = config_util.get_config(CONFIG_FILE)

        # ログ設定
        global logger
        logger = log_util.setup_logger(config["log"], config["mail"])

        logger.info(f"処理開始　バージョン：{__version__}")
        logger.info(f"設定情報：{config}")

        input_file_name = ""
        if len(sys.argv) > 1:
            input_file_name = sys.argv[1]
            logger.info(f"読込ファイル名：{input_file_name}")

        # デバッグモード
        debug_mode = config["debug_mode"]
        logger.info(f"デバッグモード：{debug_mode}")

        # 品番リストの読み込み
        code_list_excel = config["files"]["code_list_excel"]
        df_code_list = pd.read_excel(code_list_excel, dtype=str, header=0)

        # 商品名と品番の前後のスペースを削除
        df_code_list["商品名"] = df_code_list["商品名"].str.strip()
        df_code_list["品番"] = df_code_list["品番"].str.strip()

        # デバッグ用
        if debug_mode:
            input_file_name = "item_list.csv"

        # 処理時間短縮のため、CSVファイルが指定されている場合はWEBサイトから商品リストを取得しない
        if input_file_name:
            # CSVファイルから商品リストを取得
            logger.info(f"CSVファイルから商品リストを取得：{input_file_name}")
            item_list = csv_util.read_csv(input_file_name)
        else:
            # WEBサイトから商品リストを取得
            logger.info("WEBサイトから商品リストを取得")
            item_list = get_item_list(config)

        logger.info(f"商品件数：{len(item_list)}")

        # 商品リストが空の場合はエラー
        if len(item_list) == 0:
            raise Exception("WEBサイトから商品が取得できませんでした。")

        # 商品リストから商品情報を編集
        logger.info("取得した商品リストから必要な情報を抽出")
        item_datas = get_item_datas(item_list, df_code_list, config)

        # 販売管理の商品コードを追加
        logger.info("販売管理の商品コードを追加")
        item_datas = add_hanbai_code(item_datas, config)

        # excelファイル出力
        excel_file = config["files"]["scraped_data_excel"]
        logger.info(f"Excelファイルに保存：{excel_file}")
        excel_util.save_excel(item_datas, excel_file)

        logger.info("正常終了しました")
    except Exception:
        logger.exception("エラーが発生しました。処理を中止します。")
        sys.exit(1)


def get_url(page, config):
    """
    URLを取得
    :param page: ページ番号
    :param config: 設定情報
    :return: URL
    """
    # 対象URLを作成
    base_url = config["syuppin"]["url"]
    url_params = config["syuppin"]["url_params"]
    fragment = config["syuppin"]["fragment"]

    # 対象メーカー
    brands = config["syuppin"]["brand"]
    makers = [brand["maker"] for brand in brands]
    maker = ",".join(makers)

    url = base_url
    if url_params:  # パラメータがある場合
        param_list = [f"{key}={value}" for key, value in url_params.items()]
        if page > 1:
            param_list.append(f"page={page}")
        url += "?" + "&".join(param_list)
        url += f"&maker={maker}"
    if fragment:  # フラグメントがある場合
        url += f"#{fragment}"  # 対象URLを開く

    return url


def get_item_list(config):
    """
    WEBサイトから商品リストを取得（Playwright使用）
    :param config: 設定情報
    :return: 商品リスト
    """
    item_list = []
    total_items = 0
    total_pages = 0
    page_num = 1

    with sync_playwright() as p:
        # ブラウザ起動オプション設定
        browser_type = p.chromium
        browser = browser_type.launch(
            headless=False,  # ヘッドレスモードを無効化
            slow_mo=50,  # 操作間の待機時間（ミリ秒）
            args=[
                "--disable-http2",
                "--disable-quic",
                "--disable-features=NetworkService",
            ],
        )

        # コンテキスト作成
        context = browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
        )

        # ページ作成
        page = context.new_page()

        try:
            # タイムアウト設定
            page.set_default_timeout(config["selenium"]["max_wait_time"] * 1000)

            # 1ページ目を開く
            page_num = 1
            url = get_url(page_num, config)
            logger.info(f"1ページ目を開く: {url}")

            page.goto(url)

            # ページが読み込まれるまで待機
            page.wait_for_selector(".FP-resultWrap")

            # 総ページ数を取得
            total_items_text = (
                page.locator(
                    '//*[@id="itemlist"]/div[1]/div/div/p/span[1]'
                ).text_content()
                or "0"
            ).replace(",", "")
            total_items = int(total_items_text)
            logger.info(f"総商品数：{total_items}")

            # 総ページ数を計算
            total_pages = (
                total_items // int(config["syuppin"]["url_params"]["rows"]) + 1
            )
            logger.info(f"総ページ数：{total_pages}")

            # 各ページの商品を取得
            for current_page in range(1, total_pages + 1):
                if current_page > 1:
                    # 次のページに移動
                    next_url = get_url(current_page, config)
                    logger.info(f"{current_page}ページ目を開く: {next_url}")

                    # コメントアウトされた行を削除
                    page.goto(next_url, wait_until="networkidle")

                    # ページが読み込まれるまで待機
                    page.wait_for_selector(".FP-resultWrap", state="visible")

                # 商品リストを取得
                logger.info(f"{current_page}ページ目の商品を取得中...")

                # 商品リスト要素を取得
                item_elements = page.locator(".FP-resultItem:not(.nextButton)").all()
                logger.info(f"商品数: {len(item_elements)}")

                # 各商品の情報を取得
                for item_element in item_elements:
                    try:
                        # 商品名
                        product_name = (
                            item_element.locator(".FP-resultItem__name").text_content()
                            or ""
                        )

                        # 商品名が空の場合はスキップ
                        if not product_name:
                            continue

                        # 価格
                        price_element = item_element.locator(".FP-resultItem__price")
                        price = (
                            price_element.text_content()
                            if price_element.count() > 0
                            else ""
                        )

                        # 税
                        tax_element = item_element.locator(".FP-resultItem__tax")
                        tax = (
                            tax_element.text_content()
                            if tax_element.count() > 0
                            else ""
                        )

                        # 商品ID
                        id_element = item_element.locator(".FP-resultItem__id")
                        product_id = (
                            id_element.text_content() if id_element.count() > 0 else ""
                        )

                        # SOLD OUT
                        sold_out_element = item_element.locator(
                            ".FP-resultItem__priceHighlight"
                        )
                        sold_out = (
                            sold_out_element.text_content()
                            if sold_out_element.count() > 0
                            else ""
                        )

                        # 商品URL
                        product_url = ""
                        a_element = item_element.locator("a").first
                        if a_element:
                            product_url = a_element.get_attribute("href") or ""
                            if product_url:
                                product_url = f"https://www.gmt-j.com{product_url}"

                        # 画像URL
                        image_url = ""
                        img_element = item_element.locator("img").first
                        if img_element:
                            image_url = img_element.get_attribute("src") or ""
                            if image_url and image_url.startswith("/"):
                                image_url = f"https://www.gmt-j.com{image_url}"

                        logger.info(f"商品名：{product_name}, ID：{product_id}")

                        # 商品情報を追加
                        item_list.append(
                            {
                                "product_name": product_name,
                                "price": price,
                                "tax": tax,
                                "product_id": product_id,
                                "sold_out": sold_out,
                                "product_url": product_url,
                                "image_url": image_url,
                            }
                        )
                    except Exception as e:
                        logger.warning(f"商品情報取得エラー: {str(e)}")
                        continue

        except Exception as e:
            logger.exception(f"スクレイピング中にエラーが発生: {str(e)}")
        finally:
            # ブラウザを閉じる
            browser.close()

    # CSVファイル出力
    csv_util.save_csv(item_list, config["files"]["item_list_csv"])

    return item_list


def get_item_datas(item_list, df_code_list, config):
    """
    商品リストから商品情報を抽出
    :param item_list: 商品リスト
    :param df_code_list: 品番リスト
    :param config: 設定情報
    :return: 商品情報リスト
    """
    # 商品情報
    item_datas = []

    for item in item_list:
        # ブランド名（英語）
        brand_name_english = ""
        for brand in config["syuppin"]["brand"]:
            if brand["name_english"] in item["product_name"]:
                brand_name_english = brand["name_english"]
                break

        # ブランド名（日本語）
        brand_name_japanese = ""
        for brand in config["syuppin"]["brand"]:
            if brand["name_japanese"] in item["product_name"]:
                brand_name_japanese = brand["name_japanese"]
                break

        # 商品名
        product_name = item["product_name"]

        # 品番
        product_code = get_product_code(item["product_name"], df_code_list)

        # 価格
        price_text = item["price"]
        # 税
        tax_text = item["tax"]

        # 価格を編集
        # tax_textに一致する文字列をprice_textから削除
        price_text = price_text.replace(tax_text, "").strip()
        # 先頭の「￥」とカンマを削除
        price = price_text.replace("￥", "").replace(",", "").strip()

        # 税を編集
        # 括弧を削除
        tax = tax_text.replace("(", "").replace(")", "").strip()

        # ID
        product_id_text = item["product_id"]
        # IDを編集
        # ID:と括弧を削除
        product_id = product_id_text.replace("[ID:", "").replace("]", "").strip()

        # sold_out
        if item["sold_out"] == "":
            sold_out = "在庫あり"
        elif item["sold_out"] == "SOLD OUT":
            sold_out = "SOLD OUT"
        elif item["sold_out"] == "商談中":
            sold_out = "商談中"
        else:
            # SOLD OUTの場所に価格が設定されている場合は在庫あり
            sold_out = "在庫あり"
            price_text = item["sold_out"].replace(tax_text, "").strip()
            price = price_text.replace("￥", "").replace(",", "").strip()

        product_url = item["product_url"]

        image_url = item["image_url"]

        item_datas.append(
            {
                "ブランド名（英語）": brand_name_english,
                "ブランド名（日本語）": brand_name_japanese,
                "商品名": product_name,
                "品番": product_code,
                "価格": price,
                "税": tax,
                "ID": product_id,
                "SOLD OUT": sold_out,
                "商品URL": product_url,
                "画像URL": image_url,
            }
        )

    return item_datas


def get_product_code(product_name, df_code_list):
    """
    商品名から品番を取得
    :param product_name: 商品名
    :param df_code_list: 品番リスト
    :return: 品番
    """
    # 検索する商品名の前後のスペースを削除
    product_name = product_name.strip()

    # 商品名で検索
    result = df_code_list[df_code_list["商品名"] == product_name]["品番"]

    # 品番が見つからない場合は空白
    product_code = result.iloc[0] if not result.empty else ""

    return product_code


def add_hanbai_code(item_datas, config):
    """
    販売管理の商品コードを追加
    :param item_datas: 商品情報リスト
    :param config: 設定情報
    :return: 商品情報リスト
    """
    # 編集後の商品情報リスト
    result_list = []

    # 優先ブランドコードのリスト
    priority_brand_code_list = []
    for brand in config["syuppin"]["brand"]:
        if brand["priority_brand_code"]:
            if brand["priority_brand_code"]:
                priority_brand_code_list.extend(brand["priority_brand_code"])

    # SQL Serverに接続
    with db_util.get_connection(config["db"]) as conn:
        # 販売管理コード取得
        for item_data in item_datas:
            product_code = item_data["品番"]
            # 販売管理コードのリストを取得
            hanbai_code_list = get_hanbai_code_list(product_code, conn)
            # 販売管理コードを取得
            hanbai_code = get_hanbai_code(hanbai_code_list, priority_brand_code_list)
            # 品番と価格の間に挿入
            result = {}
            for key, value in item_data.items():
                result[key] = value
                if key == "品番":
                    result["販売管理コード"] = hanbai_code
            result_list.append(result)

    return result_list


def get_hanbai_code(hanbai_code_list, priority_brand_code_list):
    """
    商品コードを取得
    :param hanbai_code_list: 販売管理コードのリスト
    :param priority_brand_code_list: 優先ブランドコードのリスト
    :return: 販売管理コード
    """
    # 販売コードのリストが空の場合は空白を返す
    if not hanbai_code_list:
        return ""

    # 販売管理コードのリストが1つの場合はそのコードを返す
    if len(hanbai_code_list) == 1:
        return hanbai_code_list[0]

    # 先頭の文字列が優先ブランドコードと一致する場合は、先に一致する販売管理コードを返す
    for priority_brand_code in priority_brand_code_list:
        for hanbai_code in hanbai_code_list:
            if hanbai_code[0:3] == priority_brand_code:
                return hanbai_code

    # 複数の販売管理コードがある場合は改行コードで連結
    hanbai_code = "\n".join(hanbai_code_list)
    return hanbai_code


def get_hanbai_code_list(product_code, conn):
    """
    商品コードのリストを取得
    :param product_code: 商品コード
    :param conn: データベース接続
    :return: 販売管理コードのリスト
    """
    # 販売管理コードのリスト
    hanbai_code_list = []

    # 商品コードが空の場合は空白を返す
    if product_code == "":
        return hanbai_code_list

    # SQL文
    sql = "SELECT 商品CD FROM MSYOHINMS WHERE 型番_ﾒｰｶｰ品番 = ?"

    # SQL文を実行
    rows = db_util.fetchall(conn, sql, str(product_code))

    if rows:
        for row in rows:
            # 販売管理コードを整形
            hanbai_code = format_hanbai_code(row["商品CD"])
            # 販売管理コードをリストに追加
            hanbai_code_list.append(hanbai_code)

    return hanbai_code_list


def format_hanbai_code(hanbai_code):
    """
    販売管理コードを整形
    :param hanbai_code: 販売管理コード
    :return: 整形後の販売管理コード
    """
    # 販売管理コードをハイフンの位置で分割
    part1 = hanbai_code[0:3]
    part2 = hanbai_code[3:18]
    part3 = hanbai_code[18:23]
    part4 = hanbai_code[23:28]
    part5 = hanbai_code[28:31]
    part6 = hanbai_code[31:34]
    part7 = hanbai_code[34:37]
    part8 = hanbai_code[37:40]

    # ハイフンで連結して完成
    formatted_code = "-".join([part1, part2, part3, part4, part5, part6, part7, part8])

    return formatted_code


if __name__ == "__main__":
    main()
