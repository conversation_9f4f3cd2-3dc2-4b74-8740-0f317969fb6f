debug_mode: false

files:
  code_list_excel: "品番リスト.xlsx"
  item_list_csv: "item_list.csv"
  scraped_data_csv: "scraped_data.csv"
  scraped_data_excel: "scraped_data.xlsx"

selenium:
  options:
    - "--headless"
    - "--disable-gpu"
    - "--window-size=1920,1080"
    - "--disable-blink-features=AutomationControlled"
    - "--disable-extensions"
    - "User-Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"
  max_wait_time: 60

syuppin:
  url: "https://www.gmt-j.com/search"
  url_params:
    order: "brandname,itemDetailNameAsc"
    rows: 200
    itemwear: 0
    installments: 0
    specpanelopen: 0
    sell: "new"
  fragment: "itemlist"
  brand:
    - name_japanese: "オメガ"
      name_english: "OMEGA"
      maker: "284"
      priority_brand_code:
        - "OMS"
        - "OM1"
        - "OM "
    - name_japanese: "ブルガリ"
      name_english: "BVLGARI"
      maker: "355"
      priority_brand_code:
        - "BVS"
        - "BV "
    - name_japanese: "ショパール"
      name_english: "CHOPARD"
      maker: "300"
      priority_brand_code: []
    - name_japanese: "ティファニー"
      name_english: "TIFFANY & Co."
      maker: "339"
      priority_brand_code: []
    - name_japanese: "ブライトリング"
      name_english: "BREITLING"
      maker: "293"
      priority_brand_code: []
    - name_japanese: "モンブラン"
      name_english: "MONTBLANC"
      maker: "366"
      priority_brand_code:
        - "MBL"
    - name_japanese: "ロンジン"
      name_english: "LONGINES"
      maker: "380"
      priority_brand_code: []

log:
  level: INFO

  datetime_format: "%Y-%m-%d %H:%M:%S"

  file_handler:
    level: INFO
    format: "%(asctime)s - %(name)s - %(levelname)s - %(filename)-25s:%(lineno)-4d - %(message)s"
    path: "./log/syuppin_scraper.log"
    when: "midnight"
    interval: 1
    backup_count: 7
    encoding: "utf-8"

  console_handler:
    level: INFO
    format: "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"

  smtp_handler:
    level: ERROR
    format: "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
    subject: "【エラー】シュッピン商品情報収集"

mail:
  to_email:
    - "<EMAIL>"
  #    - "<EMAIL>"
  from_email: "<EMAIL>"
  login: "<EMAIL>"
  encrypted_password: "MAZaDV1FV1o="
  smtp_server: "smtp.office365.com"
  smtp_port: 587

db:
  driver: "{SQL Server}"
  server: "hanbai-hon01"
  database: "db_ueni"
  username: "hanbai"
  encrypted_password: "AABcCw=="
