import pyodbc

from util import crypt_util


def get_connection_string(driver, server, database, username, encrypted_password):
    """
    データベース接続文字列取得
    :param driver: ドライバー
    :param server: データベースサーバー
    :param database: データベース名
    :param username: ユーザー名
    :param encrypted_password: 暗号化されたパスワード
    :return: データベース接続文字列
    """

    # SQLServer接続文字列
    conn_str = (
        "DRIVER="
        + driver
        + ";SERVER="
        + server
        + ";DATABASE="
        + database
        + ";UID="
        + username
        + ";PWD="
        + crypt_util.decrypt(encrypted_password)
    )
    return conn_str


def get_connection(db_config):
    """
    データベース接続
    :param db_config: データベース設定情報
    :return: データベース接続オブジェクト
    """
    # SQLServer接続文字列
    conn_str = get_connection_string(
        db_config["driver"],
        db_config["server"],
        db_config["database"],
        db_config["username"],
        db_config["encrypted_password"],
    )
    # データベース接続
    conn = pyodbc.connect(conn_str)

    return conn


def fetchall(conn, sql, *params):
    """
    データ取得
    :param conn: データベース接続オブジェクト
    :param sql: SQL
    :param params: SQLのプレースホルダ用引数
    :return: 辞書形式で取得したデータのリスト
    """
    rows = []
    with conn.cursor() as cursor:
        cursor.execute(sql, params)
        # カラム名を取得
        columns = [column[0] for column in cursor.description]
        # 全行取得し、各行を辞書形式に変換
        rows = [dict(zip(columns, row)) for row in cursor.fetchall()]
    return rows


def fetchone(conn, sql, *params):
    """
    データ取得
    :param conn: データベース接続オブジェクト
    :param sql: SQL
    :param params: SQLのプレースホルダ用引数
    :return: 辞書形式で取得したデータのリスト
    :raises Exception: レコードが取得出来ない場合、複数のレコードが取得された場合
    """
    with conn.cursor() as cursor:
        cursor.execute(sql, params)

        row = cursor.fetchone()
        row2 = cursor.fetchone()

        # レコードが存在しない場合エラー
        if not row:
            raise Exception(f"レコードが存在しません。SQL: {sql}, params: {params}")

        # 複数のレコードが取得された場合エラー
        if row2:
            raise Exception(
                f"複数のレコードが取得されました。SQL: {sql}, params: {params}"
            )

        # カラム名を取得
        columns = [column[0] for column in cursor.description]

        # 辞書としてレコードを返す
        result = dict(zip(columns, row))

    return result
