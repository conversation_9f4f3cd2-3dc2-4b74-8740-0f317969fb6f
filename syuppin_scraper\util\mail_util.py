import mimetypes
import smtplib
from email.message import EmailMessage

from util import crypt_util


def send_mail(mail_config, subject, content, attach_files=None):
    """
    メール送信
    :param config: 設定情報
    :param subject: 件名
    :param content: 本文
    :param attach_files: 添付ファイル
    """
    # Microsoft 365メールを送信する
    send_ms365mail(
        to_email=mail_config["mail"]["to_email"],
        from_email=mail_config["mail"]["from_email"],
        login=mail_config["mail"]["login"],
        encrypted_password=mail_config["mail"]["encrypted_password"],
        smtp_server=mail_config["mail"]["smtp_server"],
        smtp_port=mail_config["mail"]["smtp_port"],
        subject=subject,
        content=content,
        attach_files=attach_files,
    )


def send_ms365mail(
    to_email,
    from_email,
    login,
    encrypted_password,
    smtp_server,
    smtp_port,
    subject,
    content,
    attach_files=None,
):
    """
    Microsoft 365メールを送信する
    :param to_email: 送信先メールアドレス
    :param from_email: 送信元メールアドレス
    :param login: ログインユーザー名
    :param encrypted_password: 暗号化されたパスワード
    :param smtp_server: SMTPサーバー
    :param smtp_port: SMTPポート
    :param subject: メールの件名
    :param content: メールの本文
    :param attach_files: 添付ファイルのリスト
    :return: None
    """
    # メールの設定
    msg = EmailMessage()
    msg["Subject"] = subject
    msg["From"] = from_email
    msg["To"] = to_email
    msg.set_content(content)

    # 添付ファイルの設定
    if attach_files:
        for file_path in attach_files:
            # Guess the file's MIME type based on its extension
            mime_type, _ = mimetypes.guess_type(file_path)
            if mime_type is None:
                mime_type = "application/octet-stream"  # Fallback if MIME type cannot be determined
            main_type, sub_type = mime_type.split("/", 1)

            with open(file_path, "rb") as file:
                msg.add_attachment(
                    file.read(),
                    maintype=main_type,
                    subtype=sub_type,
                    filename=file_path.split("/")[-1],
                )

    # SMTPサーバーに接続してメールを送信
    with smtplib.SMTP(smtp_server, smtp_port) as server:
        server.ehlo(smtp_server)
        server.starttls()  # Start secure connection
        server.ehlo(smtp_server)
        server.login(login, crypt_util.decrypt(encrypted_password))  # Login
        server.send_message(msg)  # Send the email
