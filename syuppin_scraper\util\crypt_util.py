import base64


def encrypt(data: str, key: str = "ue2boueki") -> str:
    """
    与えられたデータをキーとXOR演算で暗号化し、Base64でエンコードします。
    :param data: 暗号化するデータ
    :param key: 暗号化に使用するキー
    :return: 暗号化されたデータをBase64でエンコードした文字列
    """

    def xor_encrypt(data: bytes, key: bytes) -> bytes:
        """
        与えられたデータをキーとXOR演算で暗号化します。
        キーが短い場合はループして適用します。
        :param data: 暗号化するデータ
        :param key: 暗号化に使用するキー
        :return: 暗号化されたデータ
        """
        return bytes([b ^ key[i % len(key)] for i, b in enumerate(data)])

    data_bytes = data.encode("utf-8")
    key_bytes = key.encode("utf-8")
    encrypted_bytes = xor_encrypt(data_bytes, key_bytes)
    return base64.b64encode(encrypted_bytes).decode("utf-8")


def decrypt(data: str, key: str = "ue2boueki") -> str:
    """
    与えられたデータをBase64でデコードし、キーとXOR演算で復号化します。
    :param data: 復号化するデータをBase64でエンコードした文字列
    :param key: 復号化に使用するキー
    :return: 復号化されたデータ
    """

    def xor_decrypt(data: bytes, key: bytes) -> bytes:
        """
        与えられたデータをキーとXOR演算で復号化します。
        キーが短い場合はループして適用します。
        :param data: 復号化するデータ
        :param key: 復号化に使用するキー
        :return: 復号化されたデータ
        """
        return bytes([b ^ key[i % len(key)] for i, b in enumerate(data)])

    data_bytes = base64.b64decode(data)
    key_bytes = key.encode("utf-8")
    decrypted_bytes = xor_decrypt(data_bytes, key_bytes)
    return decrypted_bytes.decode("utf-8")
