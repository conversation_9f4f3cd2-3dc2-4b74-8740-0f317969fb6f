import csv


def save_csv(data, output_file):
    """
    CSVに保存
    :param data: データ
    :param output_file: 出力ファイル名
    """
    with open(output_file, "w", newline="", encoding="utf8") as csvfile:
        fieldnames = list(data[0].keys())
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames, quoting=csv.QUOTE_ALL)
        writer.writeheader()
        writer.writerows(data)


def read_csv(file_path):
    """
    CSVファイルを読み込む
    :param file_path: ファイルパス
    :return: リスト
    """
    list = []
    with open(file_path, mode="r", encoding="utf8") as csvfile:
        reader = csv.DictReader(csvfile)
        list = [row for row in reader]

    return list
