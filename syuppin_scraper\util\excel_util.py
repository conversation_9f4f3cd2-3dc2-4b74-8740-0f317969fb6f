import openpyxl


def save_excel(data, output_file):
    """
    Excelに保存
    :param data: データ
    :param output_file: 出力ファイル名
    """
    if not data:
        return

    wb = openpyxl.Workbook()
    ws = wb.active

    # 1行目にタイトル（ヘッダー）を挿入
    headers = list(data[0].keys())
    for j, header in enumerate(headers):
        ws.cell(row=1, column=j + 1, value=header)

    # 2行目以降にデータを書き込み
    for i, row in enumerate(data, start=2):  # データは2行目から
        for j, value in enumerate(row.values()):
            ws.cell(row=i, column=j + 1, value=value)

    wb.save(output_file)
